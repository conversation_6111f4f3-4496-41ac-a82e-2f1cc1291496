import { CrossTabCommunicator } from './utils/cross-tab-communicator';

declare global {
  interface Window {
    browserController: any;
  }
}

class BrowserControllerProxyError extends Error {
  constructor(
    message: string,
    public readonly code: string,
    public readonly operation?: string,
  ) {
    super(`[kazeel][browser-controller-proxy] ${message}`);
    this.name = 'BrowserControllerProxyError';
  }
}

/**
 * Target tab browser controller proxy - lightweight interface for user interaction
 * Runs in the main user interaction tab and proxies all CDP operations to control tab
 * Maintains backward compatibility with existing window.browserController interface
 */
(function () {
  const config = {
    debug: true,
  };

  let communicator: CrossTabCommunicator | null = null;
  let isInitialized = false;

  function log(...args: any[]) {
    if (config.debug) {
      console.log('[kazeel][browser-controller-proxy]', ...args);
    }
  }

  function error(...args: any[]) {
    console.error('[kazeel][browser-controller-proxy]', ...args);
  }

  /**
   * Initialize the proxy browser controller
   * This is a lightweight initialization that sets up cross-tab communication
   */
  async function init(): Promise<void> {
    log('Initializing proxy browser controller');

    if (isInitialized) {
      log('Proxy already initialized');
      return;
    }

    try {
      // Initialize cross-tab communication
      log('Setting up cross-tab communication...');
      communicator = new CrossTabCommunicator({
        channelName: 'browser-controller',
        debug: config.debug,
        timeout: 15000,
      });

      // Test communication with control tab with retries
      let attempts = 0;
      const maxAttempts = 10;

      while (attempts < maxAttempts) {
        try {
          log(`Testing connection to control tab (attempt ${attempts + 1}/${maxAttempts})...`);
          const pingResult = await communicator.sendMessage('ping');
          log('Successfully connected to control tab:', pingResult);
          isInitialized = true;
          break;
        } catch (err) {
          attempts++;
          if (attempts >= maxAttempts) {
            const connectionError = new BrowserControllerProxyError(
              `Failed to establish communication with control tab after ${maxAttempts} attempts: ${err}`,
              'CONTROL_TAB_CONNECTION_FAILED',
              'init',
            );
            error(connectionError.message, err);
            throw connectionError;
          }
          log(`Connection attempt ${attempts} failed, retrying in 500ms...`);
          await new Promise((resolve) => setTimeout(resolve, 500));
        }
      }

      log('Proxy browser controller initialized successfully');
    } catch (err) {
      if (err instanceof BrowserControllerProxyError) {
        throw err; // Re-throw our custom errors
      }

      const initError = new BrowserControllerProxyError(
        `Proxy initialization failed: ${err}`,
        'PROXY_INIT_FAILED',
        'init',
      );
      error(initError.message, err);
      throw initError;
    }
  }

  /**
   * Ensure the proxy is initialized before making calls
   */
  function ensureInitialized(): void {
    if (!isInitialized || !communicator) {
      throw new BrowserControllerProxyError(
        'Browser controller proxy not initialized. Call init() first.',
        'PROXY_NOT_INITIALIZED',
      );
    }
  }

  /**
   * Setup browser metrics (viewport, device scale, etc.)
   */
  async function setupBrowserMetrics(viewport: { width: number; height: number }): Promise<void> {
    ensureInitialized();
    log('Setting up browser metrics:', viewport);

    try {
      const result = await communicator!.sendMessage('setupBrowserMetrics', { viewport });
      if (!result.success) {
        throw new BrowserControllerProxyError(
          result.error || 'Failed to setup browser metrics',
          'SETUP_BROWSER_METRICS_FAILED',
          'setupBrowserMetrics',
        );
      }
    } catch (err) {
      if (err instanceof BrowserControllerProxyError) {
        throw err;
      }

      const metricsError = new BrowserControllerProxyError(
        `Failed to setup browser metrics: ${err}`,
        'SETUP_BROWSER_METRICS_FAILED',
        'setupBrowserMetrics',
      );
      error(metricsError.message, err);
      throw metricsError;
    }
  }

  /**
   * Dispatch mouse move event
   */
  async function dispatchMouseMove(x: number, y: number): Promise<void> {
    ensureInitialized();
    log(`Dispatching mouse move to (${x}, ${y})`);

    const result = await communicator!.sendMessage('dispatchMouseMove', { x, y });
    if (!result.success) {
      throw new Error(result.error || 'Failed to dispatch mouse move');
    }
  }

  /**
   * Dispatch mouse down event
   */
  async function dispatchMouseDown(
    x: number,
    y: number,
    button: 'left' | 'right' | 'middle' = 'left',
  ): Promise<void> {
    ensureInitialized();
    log(`Dispatching mouse down at (${x}, ${y}) with button: ${button}`);

    const result = await communicator!.sendMessage('dispatchMouseDown', { x, y, button });
    if (!result.success) {
      throw new Error(result.error || 'Failed to dispatch mouse down');
    }
  }

  /**
   * Dispatch mouse up event
   */
  async function dispatchMouseUp(
    x: number,
    y: number,
    button: 'left' | 'right' | 'middle' = 'left',
  ): Promise<void> {
    ensureInitialized();
    log(`Dispatching mouse up at (${x}, ${y}) with button: ${button}`);

    const result = await communicator!.sendMessage('dispatchMouseUp', { x, y, button });
    if (!result.success) {
      throw new Error(result.error || 'Failed to dispatch mouse up');
    }
  }

  /**
   * Dispatch mouse click event
   */
  async function dispatchMouseClick(
    x: number,
    y: number,
    button: 'left' | 'right' | 'middle' = 'left',
  ): Promise<void> {
    ensureInitialized();
    log(`Dispatching mouse click at (${x}, ${y}) with button: ${button}`);

    const result = await communicator!.sendMessage('dispatchMouseClick', { x, y, button });
    if (!result.success) {
      throw new Error(result.error || 'Failed to dispatch mouse click');
    }
  }

  /**
   * Dispatch key event
   */
  async function dispatchKeyEvent(
    type: 'keyDown' | 'keyUp' | 'char',
    key: string,
    code?: string,
  ): Promise<void> {
    ensureInitialized();
    log(`Dispatching key event: ${type} ${key}`);

    const result = await communicator!.sendMessage('dispatchKeyEvent', { type, key, code });
    if (!result.success) {
      throw new Error(result.error || 'Failed to dispatch key event');
    }
  }

  /**
   * Insert text
   */
  async function insertText(text: string): Promise<void> {
    ensureInitialized();
    log(`Inserting text: ${text}`);

    const result = await communicator!.sendMessage('insertText', { text });
    if (!result.success) {
      throw new Error(result.error || 'Failed to insert text');
    }
  }

  /**
   * Take a screenshot
   */
  async function takeScreenshot(enableGrayscale: boolean = true): Promise<{
    success: boolean;
    data?: string;
    timestamp?: number;
  }> {
    ensureInitialized();
    log('Taking screenshot');

    const result = await communicator!.sendMessage('takeScreenshot', { enableGrayscale });
    if (!result.success) {
      throw new Error(result.error || 'Failed to take screenshot');
    }

    return result;
  }

  /**
   * Capture screenshot with grayscale conversion
   * Takes a screenshot and applies grayscale conversion for consistent processing
   * @returns Screenshot data with grayscale conversion applied
   */
  async function captureScreenshotWithGrayscale(): Promise<{
    success: boolean;
    data?: string;
    timestamp?: number;
    processingTime?: number;
    error?: string;
  }> {
    ensureInitialized();
    log('Capturing screenshot with grayscale conversion');

    const startTime = performance.now();

    try {
      // Take screenshot with grayscale enabled
      const screenshotResult = await communicator!.sendMessage('takeScreenshot', {
        enableGrayscale: true,
      });

      if (!screenshotResult.success) {
        throw new BrowserControllerProxyError(
          screenshotResult.error || 'Failed to capture screenshot',
          'SCREENSHOT_FAILED',
          'captureScreenshotWithGrayscale',
        );
      }

      const totalProcessingTime = performance.now() - startTime;

      log(`Grayscale screenshot completed in ${totalProcessingTime.toFixed(2)}ms`);

      return {
        success: true,
        data: screenshotResult.data,
        timestamp: screenshotResult.timestamp,
        processingTime: totalProcessingTime,
      };
    } catch (err) {
      if (err instanceof BrowserControllerProxyError) {
        throw err;
      }

      const screenshotError = new BrowserControllerProxyError(
        `Failed to capture screenshot with grayscale: ${err}`,
        'SCREENSHOT_WITH_GRAYSCALE_FAILED',
        'captureScreenshotWithGrayscale',
      );
      error(screenshotError.message, err);
      throw screenshotError;
    }
  }

  /**
   * Handle input event (for backward compatibility)
   * Sends the entire event to the control tab for comprehensive handling
   */
  async function handleInputEvent(event: any): Promise<void> {
    ensureInitialized();
    log('Handling input event:', event.type, event);

    const result = await communicator!.sendMessage('handleInputEvent', { event });
    if (!result.success) {
      throw new Error(result.error || 'Failed to handle input event');
    }
  }

  /**
   * Request new frame generation
   */
  async function requestNewFrame(): Promise<void> {
    ensureInitialized();
    log('Requesting new frame');

    const result = await communicator!.sendMessage('requestNewFrame');
    if (!result.success) {
      throw new Error(result.error || 'Failed to request new frame');
    }
  }

  /**
   * Trigger mouse movement for frame generation
   */
  async function triggerMouseMovement(): Promise<void> {
    ensureInitialized();
    log('Triggering mouse movement');

    const result = await communicator!.sendMessage('triggerMouseMovement');
    if (!result.success) {
      throw new Error(result.error || 'Failed to trigger mouse movement');
    }
  }

  /**
   * Ping the control tab (for testing connectivity)
   */
  async function ping(): Promise<any> {
    ensureInitialized();
    log('Pinging control tab');

    const result = await communicator!.sendMessage('ping');
    return result;
  }

  // Expose public API with backward compatibility
  (globalThis as any).browserController = {
    init,
    setupBrowserMetrics,
    dispatchMouseMove,
    dispatchMouseDown,
    dispatchMouseUp,
    dispatchMouseClick,
    dispatchKeyEvent,
    insertText,
    takeScreenshot,
    captureScreenshotWithGrayscale,
    handleInputEvent,
    requestNewFrame,
    triggerMouseMovement,
    ping,
  };

  log('Proxy browser controller script loaded');
})();
