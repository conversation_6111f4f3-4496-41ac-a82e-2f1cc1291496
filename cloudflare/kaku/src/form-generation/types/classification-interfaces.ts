/**
 * Classification interfaces for screen classification
 */

export interface ClassificationResult {
  screenInfo: ClassificationMetadata;
}

export interface ClassificationMetadata {
  title: string;
  instruction: string;
  authState: 'authenticated' | 'not-authenticated';
  errors: string[] | null;
  errorsCount: number | null;
  classificationReasoning: string;
  screenClass: ScreenClass;
  screenCode: string | null;
}

export type ScreenClass =
  | 'profile-management-screen'
  | 'multi-factor-code-verification-screen'
  | 'multi-factor-push-approval-screen'
  | 'multi-factor-multiple-options-screen'
  | 'passkey-screen'
  | 'captcha-screen'
  | 'loading-screen'
  | 'trust-device-screen'
  | 'other'
  | 'logged-in-screen';
